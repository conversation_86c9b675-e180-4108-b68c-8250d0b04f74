package com.btpns.fin.controller;

import com.btpns.fin.helper.*;
import com.btpns.fin.helper.ResponseStatus;
import com.btpns.fin.model.*;
import com.btpns.fin.model.entity.*;
import com.btpns.fin.model.response.*;
import com.btpns.fin.repository.IMsEmployeeDirectorRepository;
import com.btpns.fin.service.*;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.nio.file.Files;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.btpns.fin.helper.CommonHelper.*;
import static com.btpns.fin.helper.Constants.*;
import static com.btpns.fin.helper.ResponseHelper.*;
import static com.btpns.fin.helper.ResponseHelper.responseHttpSuccessWithStatus;
import static com.btpns.fin.helper.ResponseStatus.*;

@RestController
@RequestMapping(value = "tema/fuid")
@CrossOrigin("*")
public class TrxFuidRequestController {
    private static final Logger logger = LoggerFactory.getLogger(TrxFuidRequestController.class);

    @Autowired
    TrxFuidRequestService trxFuidRequestService;

    @Autowired
    TrxFuidApprovalService trxFuidApprovalService;

    @Autowired
    TrxAudittrailService trxAudittrailService;

    @Autowired
    MsSystemParamService msSystemParamService;

    @Autowired
    MsTemaApplicationService msTemaApplicationService;

    @Autowired
    MsEmployeeService msEmployeeService;

    @Autowired
    TrxFuidRequestAplikasiService trxFuidRequestAplikasiService;

    @Autowired
    TrxDelegationService trxDelegationService;

    @Autowired
    TrxPUKVendorService trxPUKVendorService;

    @Autowired
    TrxUpmRoleService trxUpmRoleService;

    @Autowired
    MsEmployeeDirectorService msEmployeeDirectorService;

    @Autowired
    EmailNotificationService emailNotificationService;

    @Autowired
    Mapper mapper;

    @Autowired
    IMsEmployeeDirectorRepository iMsEmployeeDirectorRepository;

    @Autowired
    private final Gson gson = GsonUtil.getInstance().getGson();

    @GetMapping(value = "/{ticketId}")
    public ResponseEntity<ResponseModel<TrxFuidRequestDetailModel>> getTrxFuidRequestByTicketId(@PathVariable("ticketId") String ticketId,
                                                                                                @RequestParam("isHitProspera") Boolean isHitProspera,
                                                                                                @RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = iMsEmployeeDirectorRepository.checkNikDirector(profile.getProfile().getPreferred_username());
            logger.info("Received <<< getTrxFuidRequestByTicketId ticketId {} from {}", ticketId, getProfile(gson, profile));
            boolean isInPuk1 = false, isInPuk2 = false, isInUpmProcess = false, isInUpmChecker = false;
            TrxFuidRequest savedTrxFuidRequest = trxFuidRequestService.getTrxFuidRequestByTicketId(ticketId);
            //validation access based on token
            boolean passValidation = false;
            passValidation = CommonHelper.validateGetDetail(
                    nikRequester,
                    savedTrxFuidRequest.getNikRequester(),
                    savedTrxFuidRequest.getTrxFuidApproval().getPuk1NIK(),
                    savedTrxFuidRequest.getTrxFuidApproval().getPuk2NIK(),
                    trxUpmRoleService.getTrxUpmRole(nikRequester));
            if(passValidation) {
                ResponseModel<TrxFuidRequestDetailModel> response = trxFuidRequestService.getTrxFuidRequestDetail(ticketId, isHitProspera, savedTrxFuidRequest, authorization);
                logger.info("Response >>> getTrxFuidRequestByTicketId : {}", gson.toJson(response.getDetails()));
                return ResponseEntity.ok(response);
            } else {
                return responseHttpSuccessWithStatus(TYPE_DETAIL_FUID_GET, FORBIDDEN, "getTrxFuidRequestByTicketId", gson);
            }
        } catch (Exception e) {
            return responseInternalServerError("getTrxFuidRequestByTicketId", e);
        }
    }

    @GetMapping(value = "/{ticketId}/download")
    public ResponseEntity<ResponseModel<ResUploadModel>> getTrxFuidRequestByTicketIdPdf(@PathVariable("ticketId") String ticketId,
                                                                                        @RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = iMsEmployeeDirectorRepository.checkNikDirector(profile.getProfile().getPreferred_username());
            logger.info("Received <<< getTrxFuidRequestByTicketIdPdf ticketId {} from {}", ticketId, getProfile(gson, profile));
            TrxFuidRequest savedTrxFuidRequest = trxFuidRequestService.getTrxFuidRequestByTicketId(ticketId);
            //validation access based on token
            boolean passValidation = false;
            passValidation = CommonHelper.validateGetDetail(
                    nikRequester,
                    savedTrxFuidRequest.getNikRequester(),
                    savedTrxFuidRequest.getTrxFuidApproval().getPuk1NIK(),
                    savedTrxFuidRequest.getTrxFuidApproval().getPuk2NIK(),
                    trxUpmRoleService.getTrxUpmRole(nikRequester));
            if(passValidation) {
                TrxFuidRequestDetailModel tfrdm = trxFuidRequestService.buildTrxFuidRequestDetailModel(savedTrxFuidRequest, ticketId);
                logger.info("Response >>> getTrxFuidRequestByTicketIdPdf : {}", gson.toJson(tfrdm));

                ResUploadModel detailFuidReqPdf = trxFuidRequestService.generateDetailFuidReqPdf(tfrdm);
                return ResponseEntity.ok(buildResponse(SUCCESS, detailFuidReqPdf));
            } else {
                return responseHttpSuccessWithStatus(TYPE_DETAIL_FUID_DOWNLOAD, FORBIDDEN, "getTrxFuidRequestByTicketIdPdf", gson);
            }
        } catch (Exception e) {
            return responseInternalServerError("getTrxFuidRequestByTicketIdPdf", e);
        }
    }

    @GetMapping(value = "/{ticketId}/direct-download")
    public ResponseEntity<Resource> directDownloadFuidRequestByTicketIdPdf(@PathVariable("ticketId") String ticketId,
                                                                           @RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            String nikRequester = iMsEmployeeDirectorRepository.checkNikDirector(profile.getProfile().getPreferred_username());
            logger.info("Received <<< directDownloadFuidRequestByTicketIdPdf ticketId {} from {}", ticketId, getProfile(gson, profile));
            TrxFuidRequest savedTrxFuidRequest = trxFuidRequestService.getTrxFuidRequestByTicketId(ticketId);
            //validation access based on token
            boolean passValidation = false;
            passValidation = CommonHelper.validateGetDetail(
                    nikRequester,
                    savedTrxFuidRequest.getNikRequester(),
                    savedTrxFuidRequest.getTrxFuidApproval().getPuk1NIK(),
                    savedTrxFuidRequest.getTrxFuidApproval().getPuk2NIK(),
                    trxUpmRoleService.getTrxUpmRole(nikRequester));
            if(passValidation) {
                TrxFuidRequestDetailModel tfrdm = trxFuidRequestService.buildTrxFuidRequestDetailModel(savedTrxFuidRequest, ticketId);
                logger.info("Response >>> directDownloadFuidRequestByTicketIdPdf : {}", gson.toJson(tfrdm));

                ResFileDownload response = trxFuidRequestService.directDownloadPdfDetailFuid(tfrdm);
                return responseDownloadFile("directDownloadFuidRequestByTicketIdPdf", response);
            } else {
                return responseHttpSuccessWithStatus(TYPE_DETAIL_FUID_DOWNLOAD, FORBIDDEN, "directDownloadFuidRequestByTicketIdPdf", gson);
            }
        } catch (Exception e) {
            return responseInternalServerError("directDownloadFuidRequestByTicketIdPdf", e);
        }
    }

    private ResponseModel<ResUploadModel> buildResponse(ResponseStatus status, ResUploadModel result) {
        ResponseModel<ResUploadModel> response = new ResponseModel<>();

        response.setType(TYPE_DETAIL_FUID_DOWNLOAD);
        response.setStatus(status.getCode());
        response.setStatusDesc(status.getValue());
        response.setDetails(result);

        return response;
    }

    @GetMapping(value = "/own-ticket")
    public ResponseEntity<ListOwnTicketFuidModel> getTrxFuidReqJoinApproval(@RequestParam("page") int pageNumber, @RequestParam("limit") int pageSize, @RequestHeader("XToken") String authorization){
        try{
            Token profile = new Token(authorization);
            logger.info("Received <<< getTrxFuidReqJoinApproval page {} limit {} from {}", pageNumber, pageSize, getProfile(gson, profile));
            String nikRequester = iMsEmployeeDirectorRepository.checkNikDirector(profile.getProfile().getPreferred_username());

            //offset start from 0
            int pageNumMin1 = pageNumber - 1;
            Page<TrxFuidRequest> pageTFR = trxFuidRequestService.getTrxFuidReqJoinApproval(nikRequester, pageNumMin1, pageSize);
            List<TrxFuidRequest> listTrxFuidRequest = pageTFR.getContent();

            ListOwnTicketFuidModel listOwnTicketFuidModel = new ListOwnTicketFuidModel();
            List<OwnTicketFuidModel> tempList = new ArrayList<OwnTicketFuidModel>();
            Iterator<TrxFuidRequest> iterator = listTrxFuidRequest.iterator();
            while(iterator.hasNext()){
                TrxFuidRequest tfr = iterator.next();
                OwnTicketFuidModel otfm = Mapper.toOwnTicketFuidModel(tfr);
                String ecurrState = msSystemParamService.getMsSystemParamDetail(otfm.getStatus().getKeyStatus()).getParamDetailDesc();
                otfm.getStatus().setValueStatus(ecurrState);
                tempList.add(otfm);
            }
            listOwnTicketFuidModel.setOwnTicket(tempList);
            listOwnTicketFuidModel.setPage(pageNumber);
            listOwnTicketFuidModel.setLimit(pageSize);
            listOwnTicketFuidModel.setTotalPages(pageTFR.getTotalPages());
            listOwnTicketFuidModel.setTotalItems(pageTFR.getTotalElements());

            logger.info("Response >>> getTrxFuidReqJoinApproval : with size {}", listOwnTicketFuidModel.getOwnTicket().size());
            return ResponseEntity.ok(listOwnTicketFuidModel);
        } catch (Exception e) {
            logger.error("Fail to getTrxFuidReqJoinApproval ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "/waiting-ticket")
    public ResponseEntity<ListWaitingTicketFuidModel> getTrxFuidReqJoinApprovalWait(@RequestParam("page") int pageNumber, @RequestParam("limit") int pageSize, @RequestHeader("XToken") String authorization){
        try{
            Token profile = new Token(authorization);
            logger.info("Received <<< getTrxFuidReqJoinApprovalWait page {} limit {} from {}", pageNumber, pageSize, getProfile(gson, profile));
            String nikRequester = iMsEmployeeDirectorRepository.checkNikDirector(profile.getProfile().getPreferred_username());

            //offset start from 0
            int pageNumMin1 = pageNumber - 1;
            Page<TrxFuidRequest> pageTFR = trxFuidRequestService.getTrxFuidReqJoinApprovalWait(nikRequester, pageNumMin1, pageSize);
            List<TrxFuidRequest> listTrxFuidRequest = pageTFR.getContent();
            ListWaitingTicketFuidModel listWaitTicketFuidModel = new ListWaitingTicketFuidModel();
            List<WaitingTicketFuidModel> tempList = new ArrayList<WaitingTicketFuidModel>();
            Iterator<TrxFuidRequest> iterator = listTrxFuidRequest.iterator();
            while(iterator.hasNext()){
                TrxFuidRequest tfr = iterator.next();
                WaitingTicketFuidModel wtfm = Mapper.toWaitTicketFuidModel(tfr);
                String ecurrState = msSystemParamService.getMsSystemParamDetail(wtfm.getStatus().getKeyStatus()).getParamDetailDesc();
                wtfm.getStatus().setValueStatus(ecurrState);
                tempList.add(wtfm);
            }
            listWaitTicketFuidModel.setWaitingTicket(tempList);
            listWaitTicketFuidModel.setPage(pageNumber);
            listWaitTicketFuidModel.setLimit(pageSize);
            listWaitTicketFuidModel.setTotalPages(pageTFR.getTotalPages());
            listWaitTicketFuidModel.setTotalItems(pageTFR.getTotalElements());

            logger.info("Response >>> getTrxFuidReqJoinApprovalWait : with size {}", listWaitTicketFuidModel.getWaitingTicket().size());
            return ResponseEntity.ok(listWaitTicketFuidModel);
        } catch (Exception e) {
            logger.error("Fail to getTrxFuidReqJoinApprovalWait ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @PostMapping()
    public ResponseEntity<ResTrxFuidRequestModel> saveTrxFuidRequest(@RequestBody TrxFuidRequestModel trxFuidRequestModel, @RequestHeader("XToken") String authorization) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< saveTrxFuidRequest with data {} from {}", gson.toJson(trxFuidRequestModel), getProfile(gson, profile));
            String nikRequester = iMsEmployeeDirectorRepository.checkNikDirector(profile.getProfile().getPreferred_username());

            if (!isValidDataLength(trxFuidRequestModel)) {
                logger.info("Response >>> saveTrxFuidRequest : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }

            encodeRequestFuid(trxFuidRequestModel);
            if (isExistInInterval(nikRequester)) {
                logger.info("Response >>> saveTrxFuidRequest : {}", getHttpStatusDetail(HttpStatus.TOO_MANY_REQUESTS));
                return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
            }
            if (!isValidPUK(nikRequester, trxFuidRequestModel)) {
                logger.info("Response >>> saveTrxFuidRequest : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
            DateTimeFormatter dateFormater3 = DateTimeFormatter.ofPattern("dd MMMM yyyy HH:mm:ss");
            boolean isUpdated = false;
            String ticketId = "DF9912280001";
            if (trxFuidRequestModel.getTicketId() == null || trxFuidRequestModel.getTicketId() == "") {
                String currDtTicket = new SimpleDateFormat("yyMMdd").format(new Date());
                //cek last ticket di db ada ngga
                logger.info("getLastTicketId: " + trxFuidRequestService.getLastTicketId());
                if (trxFuidRequestService.getLastTicketId() == null) {
                    ticketId = "FU" + currDtTicket + "0001";
                } else {
                    String lastTicketId = trxFuidRequestService.getLastTicketId();
                    String strlastDate = lastTicketId.substring(2, 8);
                    logger.info("strlastDate: " + strlastDate);
                    //cek tanggalnya sama ngga sama currentdate
                    if (strlastDate.equals(currDtTicket)) {
                        String strTicketNum = lastTicketId.substring(8, 12);
                        logger.info("strTicketNum: " + strTicketNum);
                        Integer ticketNum = Integer.parseInt(strTicketNum) + 1;
                        ticketId = "FU" + currDtTicket + String.format("%04d", ticketNum);
                    } else {
                        ticketId = "FU" + currDtTicket + "0001";
                    }
                }
            } else {
                //update ticket if rejected
                isUpdated = true;
                ticketId = trxFuidRequestModel.getTicketId();
            }
            trxFuidRequestModel.setTicketId(ticketId);
            trxFuidRequestModel.getFuid().setNikRequester(nikRequester);

            //check duplicate request
            boolean isSuccesRequest = false;
            if (!isDuplicateRequestId(trxFuidRequestModel.getRequestId())) {
                String currState = "";
                String puk1State = "";
                String puk2State = "";

                trxFuidRequestModel = trxFuidRequestService.enrichDataByAplikasiAndAlasan(trxFuidRequestModel);

                TrxFuidRequest tfr = Mapper.toTrxFuidRequestEntity(trxFuidRequestModel);
                tfr.setNikRequester(nikRequester);
                tfr.setDataNamaCabang(trxFuidRequestService.getNamaCabangMms(tfr.getDataKodeCabang()));
                TrxFuidRequest savedTFR = null;
                if (isUpdated) {
                    savedTFR = trxFuidRequestService.updateTrxFuidRequest(tfr);
                } else {
                    savedTFR = trxFuidRequestService.saveTrxFuidRequest(tfr);
                }

                // Check if save/update operation was successful
                if (savedTFR == null) {
                    logger.error("Failed to save/update TrxFuidRequest for ticketId: {}. Record may already exist.", tfr.getTicketId());
                    logger.info("Response >>> saveTrxFuidRequest : {}", getHttpStatusDetail(HttpStatus.CONFLICT));
                    return ResponseEntity.status(HttpStatus.CONFLICT).build();
                }

                if (isUpdated) {
                    trxFuidRequestAplikasiService.deleteTrxFuidRequestAplikasiByTicketId(ticketId);
                }
                if (trxFuidRequestModel.getFuid().getAplikasi().size() > 0) {
                    List<String> lAplikasi = trxFuidRequestModel.getFuid().getAplikasi();
                    for (String aplikasi : lAplikasi) {
                        MsTemaApplication msta = msTemaApplicationService.getMsTemaApplication(aplikasi);
                        TrxFuidRequestAplikasi tfrap = Mapper.toTrxFuidRequestAplikasiEntity(ticketId, msta);
                        TrxFuidRequestAplikasi savedTfrap = trxFuidRequestAplikasiService.saveTrxFuidRequestAplikasi(tfrap);
                    }
                }

                TrxFuidApproval tfra = Mapper.toTrxFuidApprovalEntity(trxFuidRequestModel.getTicketId());
                //validation aplikasi = upload spesimen maka curr status = approved
                String puk1Nik = "", puk2Nik = "";
                boolean isUploadSpesimen = isUploadSpesimen(trxFuidRequestModel);
                if (!isUploadSpesimen) {
                    //is not upload spesimen
                    puk1Nik = trxFuidRequestModel.getFuid().getApproval().getPuk1() == null ? "" : trxFuidRequestModel.getFuid().getApproval().getPuk1();
                    if (puk1Nik != "") {
                        //check delegation
                        if (trxDelegationService.getTrxDelegationByNikRequester(puk1Nik) != null) {
                            tfra.setPuk1DelegationId(trxDelegationService.getTrxDelegationByNikRequester(puk1Nik).getDelegationId());
                            puk1Nik = trxDelegationService.getTrxDelegationByNikRequester(puk1Nik).getNikDelegation();
                        }
                        MsEmployee puk1 = msEmployeeService.getEmployeeByNik(puk1Nik);
                        currState = CURR_STATUS_WAITING_PUK1;
                        puk1State = PUK1_STATUS_WAITING;
                        tfra.setPuk1NIK(puk1Nik);
                        tfra.setPuk1Name(puk1.getFullName());
                        tfra.setPuk1Occupation(puk1.getOccupationDesc());
                        tfra.setPuk1Dt(tfr.getCreateDateTime());
                        tfra.setPuk1Status(puk1State);
                        tfra.setPuk1Notes("");
                    }
                    puk2Nik = trxFuidRequestModel.getFuid().getApproval().getPuk2() == null ? "" : trxFuidRequestModel.getFuid().getApproval().getPuk2();
                    if (puk2Nik != "") {
                        //check delegation
                        if (trxDelegationService.getTrxDelegationByNikRequester(puk2Nik) != null) {
                            tfra.setPuk2DelegationId(trxDelegationService.getTrxDelegationByNikRequester(puk2Nik).getDelegationId());
                            puk2Nik = trxDelegationService.getTrxDelegationByNikRequester(puk2Nik).getNikDelegation();
                        }
                        MsEmployee puk2 = msEmployeeService.getEmployeeByNik(puk2Nik);
                        puk2State = PUK2_STATUS_WAITING;
                        tfra.setPuk2NIK(puk2Nik);
                        tfra.setPuk2Name(puk2.getFullName());
                        tfra.setPuk2Occupation(puk2.getOccupationDesc());
                        tfra.setPuk2Dt(tfr.getCreateDateTime());
                        tfra.setPuk2Status(puk2State);
                        tfra.setPuk2Notes("");
                    }
                } else {
                    //is upload spesimen
                    currState = CURR_STATUS_APPROVED;
                }
                tfra.setCurrentState(currState);
                TrxFuidApproval savedTFRA = null;
                if (isUpdated) {
                    savedTFRA = trxFuidApprovalService.updateTrxFuidApproval(tfra);
                } else {
                    savedTFRA = trxFuidApprovalService.saveTrxFuidApproval(tfra);
                }

                if (savedTFR.getTujuan().equals(TUJUAN_PENDAFTARAN_BARU)
                        && savedTFR.getAlasan().equals(ALASAN_KARYAWAN_BARU)
                        && savedTFR.getTipeKaryawanBaru().equals(TIPE_KARYAWAN_BARU_NON_FTE)
                        && trxFuidRequestModel.getFuid().getData().getNIK() != null
                        && trxFuidRequestModel.getFuid().getData().getNikPUKVendor() != null) {
                    TrxPUKVendor trxPUKVendor = new TrxPUKVendor();
                    trxPUKVendor.setMasaBerlakuSampai(savedTFR.getMasaBerlakuSampai());
                    trxPUKVendor.setNikVendor(trxFuidRequestModel.getFuid().getData().getNIK());
                    trxPUKVendor.setNameVendor(trxFuidRequestModel.getFuid().getData().getNamaLengkap());
                    trxPUKVendor.setOccupationVendor(trxFuidRequestModel.getFuid().getData().getJabatan());
                    trxPUKVendor.setOccupationDescVendor(trxFuidRequestModel.getFuid().getData().getJabatan());
                    trxPUKVendor.setNikPUK(trxFuidRequestModel.getFuid().getData().getNikPUKVendor());
                    LocalDateTime currDt = LocalDateTime.now();
                    if (isUpdated || trxPUKVendorService.findByNikVendor(trxFuidRequestModel.getFuid().getData().getNIK()) != null) {
                        trxPUKVendor.setUpdateDateTime(currDt);
                        trxPUKVendorService.updateTrxPUKVendor(trxPUKVendor);
                    } else {
                        trxPUKVendor.setCreateDatetime(currDt);
                        trxPUKVendor.setUpdateDateTime(currDt);
                        trxPUKVendorService.saveTrxPUKVendor(trxPUKVendor);
                    }
                }

                TrxAudittrail ta = Mapper.toTrxAudittrailEntity(trxFuidRequestModel.getTicketId());
                ta.setNik(nikRequester);
                ta.setAction(currState);
                //json additional info
                TimelineStatusModel tsm = new TimelineStatusModel();
                if (isUpdated) {
                    tsm.setStatus(TIMELINE_STATUS_RESUBMIT_TICKET);
                } else {
                    tsm.setStatus(TIMELINE_STATUS_CREATE_TICKET);
                }
                tsm.setPic(TIMELINE_PIC_USER + nikRequester + " - " + profile.getProfile().getName());
                tsm.setTimestamp(dateFormater3.format(ta.getCreateDateTime()));
                ta.setAdditionalInfo(new Gson().toJson(tsm));
                TrxAudittrail savedTa = trxAudittrailService.saveTrxAudittrail(ta);

                //send email
                if (puk1Nik != "" || isUploadSpesimen) {
                    TrxFuidRequest email = mapper.buildFormatedContentEmailTicketFU(savedTFR, savedTFRA);
                    if (isUploadSpesimen){
                        sendEmailRequestSpesimen(profile, email);
                    }else {
                        MsEmployee waitingApprovalPUK = msEmployeeService.getMsEmployeeByNik(savedTFRA.getPuk1NIK());
                        sendEmailRequest(profile, email, puk1Nik, validateCCNikDirectPuk(email), waitingApprovalPUK);
                    }
                }

                if (savedTFR != null && savedTFRA != null && savedTa != null) {
                    isSuccesRequest = true;
                }
            }

            ResTrxFuidRequestModel resTrxFuidRequestModel = new ResTrxFuidRequestModel();
            resTrxFuidRequestModel.setType(trxFuidRequestModel.getType());
            resTrxFuidRequestModel.setTicketId(trxFuidRequestModel.getTicketId());

            if(isSuccesRequest){
                resTrxFuidRequestModel.setStatus(SUCCESS.getCode());
                resTrxFuidRequestModel.setStatusDesc(SUCCESS.getValue());
            } else {
                resTrxFuidRequestModel.setStatus(FAILED.getCode());
                resTrxFuidRequestModel.setStatusDesc(FAILED.getValue());
            }
            logger.info("Response >>> saveTrxFuidRequest : {}", gson.toJson(resTrxFuidRequestModel));
            return ResponseEntity.ok(resTrxFuidRequestModel);
        } catch (Exception e) {
            logger.error("Fail to post TrxFuidRequest ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    private void sendEmailRequestSpesimen(Token profile, TrxFuidRequest savedTFR) {
        createRequestSpesimen(profile.getProfile().getPreferred_username(), savedTFR);
    }

    private void createRequestSpesimen(String userNIK, TrxFuidRequest trxFuidRequest) {
        emailNotificationService.sendSpesimenToUpmTemaNotification(userNIK, trxFuidRequest);
    }

    private void sendEmailRequest(Token profile, TrxFuidRequest savedTFR, String puk1Nik, String ccNikDirectPuk, MsEmployee waitingApprovalPUK) {
        //send to requester
        createRequestAsync(profile.getProfile().getPreferred_username(), savedTFR, waitingApprovalPUK);
        //send to approval
        createApprovalAsync(puk1Nik, savedTFR, ccNikDirectPuk, waitingApprovalPUK);
    }

    public void createRequestAsync(String userNik, TrxFuidRequest trxFuidRequest, MsEmployee waitingApprovalPUK) {
        emailNotificationService.sendCreateRequestFuidToUpmTemaNotification(userNik, trxFuidRequest, waitingApprovalPUK);
    }

    public void createApprovalAsync(String pukNik, TrxFuidRequest trxFuidRequest, String ccNikDirectPuk, MsEmployee waitingPUK) {
        //check director
        HashMap<String, String> mapPUKDirector = msEmployeeDirectorService.isPukDirectorByNikOptima(pukNik);
        emailNotificationService.sendCreateApprovalFuidToUpmTemaNotification(pukNik, trxFuidRequest, ccNikDirectPuk, waitingPUK, mapPUKDirector);
    }

    @GetMapping(value = "/token")
    public ResponseEntity<String> getTokenData(HttpServletResponse response,
                                              @RequestHeader("XToken") String authorization) throws Exception {
        try {
            Token profile = new Token(authorization);
            //String dataUnknown = profile.getDataUnknown();
            HashMap<String, String> mapProfile = new HashMap<>();
            mapProfile.put("header", profile.getDataHeader());
            mapProfile.put("profile", profile.getDataProfile());
            mapProfile.put("countChunks", profile.getCountChunks());
            Gson gson = new Gson();
            return ResponseEntity.ok(gson.toJson(mapProfile));
        } catch (Exception e){
            logger.error("Get token error ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    @GetMapping(value = "/getbase64")
    public ResponseEntity<String> getBase64(@RequestHeader("XToken") String authorization) throws Exception {
        try {
            //Upload
            File file = new File("C:/Test/test.xlsx");
            byte[] byteFile = Files.readAllBytes(file.toPath());
            String sBase64 = Base64.getEncoder().encodeToString(byteFile);
            HashMap<String, String> output = new HashMap<>();
            output.put("imagebase64", sBase64);
            Gson gson = new Gson();

            //Download
            File fileD = new File("C:/Test/test1.xlsx");
            FileOutputStream fos = new FileOutputStream(fileD);
            byte[] decoder = Base64.getDecoder().decode(sBase64);
            fos.write(decoder);

            return ResponseEntity.ok(gson.toJson(output));
        } catch (Exception e){
            logger.error("getBase64 error ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    public boolean isDuplicateRequestId(String requestId) throws ParseException {
        boolean isDuplicate = false;
        //jika requestId ada di db return true, lainnya false
        if(trxFuidRequestService.checkDuplicateRequestId(requestId) != null){
            isDuplicate = true;
        }
        return isDuplicate;
    }

    public boolean isExistInInterval(String nik) {
        return trxFuidRequestService.existInInterval(nik, 10);
    }

    private String validateCCNikDirectPuk(TrxFuidRequest savedTFR) {
        String nikDirectPuk = "";
        TrxFuidApproval savedTFA = savedTFR.getTrxFuidApproval();
        MsEmployee directPUK = msEmployeeService.getDirectPUK(savedTFR.getNikRequester());
        if (directPUK != null) {
            boolean isCCDirectPuk = false;
            if (!savedTFA.getPuk1NIK().equals(directPUK.getNik())) {
                isCCDirectPuk = true;
            }
            if (isCCDirectPuk) {
                nikDirectPuk = directPUK.getNik();
            }
        }
        return nikDirectPuk;
    }

    public boolean isValidPUK(String nikRequester, TrxFuidRequestModel trxFuidRequestModel) {
        //validation puk empty
        if(isUploadSpesimen(trxFuidRequestModel) && trxFuidRequestModel.getFuid().getAttachment().size() == 0){
            return false;
        } else if(!isUploadSpesimen(trxFuidRequestModel)
                && trxFuidRequestModel.getFuid().getApproval().getPuk1() == null
                && trxFuidRequestModel.getFuid().getApproval().getPuk2() == null){
            return false;
        }
        if(!isUploadSpesimen(trxFuidRequestModel) && trxFuidRequestModel.getFuid().getApproval().getPuk1() != null){
            if(trxFuidRequestModel.getFuid().getApproval().getPuk1().trim().isEmpty()){
                return false;
            }
        }
        if(!isUploadSpesimen(trxFuidRequestModel) && trxFuidRequestModel.getFuid().getApproval().getPuk2() != null){
            if(trxFuidRequestModel.getFuid().getApproval().getPuk2().trim().isEmpty()){
                return false;
            }
        }

        if (nikRequester.equalsIgnoreCase(trxFuidRequestModel.getFuid().getApproval().getPuk1())
                || (trxFuidRequestModel.getFuid().getApproval().getPuk2() != null && nikRequester.equalsIgnoreCase(trxFuidRequestModel.getFuid().getApproval().getPuk2()))) {
            return false;
        }
        return true;
    }

    public boolean isUploadSpesimen(TrxFuidRequestModel trxFuidRequestModel){
        boolean isUploadSpesimen = false;
        if (trxFuidRequestModel.getFuid().getAplikasi().size() > 0) {
            List<String> lAplikasi = trxFuidRequestModel.getFuid().getAplikasi();
            isUploadSpesimen = lAplikasi.contains(KEY_UPLOAD_SPESIMEN) && TUJUAN_UPLOAD_SPECIMEN.equals(trxFuidRequestModel.getFuid().getTujuan());
        }
        return isUploadSpesimen;
    }

    @PostMapping(value = "/prospera")
    public ResponseEntity<ResponseModel<ResTrxFuidRequest>> saveTrxFuidRequestProspera(@RequestHeader("XToken") String authorization,
                                                                                       @RequestBody TrxFuidRequestModel trxFuidRequestModel) {
        try {
            Token profile = new Token(authorization);
            logger.info("Received <<< saveTrxFuidRequestProspera with data {} from {}", gson.toJson(trxFuidRequestModel), getProfile(gson, profile));
            String nikRequester = profile.getProfile().getPreferred_username();

            if (!isValidDataLength(trxFuidRequestModel)) {
                logger.info("Response >>> saveTrxFuidRequestProspera : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }

            encodeRequestFuid(trxFuidRequestModel);
            if (!isExistInInterval(nikRequester)) {
                if (isValidPUK(nikRequester, trxFuidRequestModel)) {
                    ResponseModel<ResTrxFuidRequest> response = trxFuidRequestService.saveTrxFuidRequestProspera(trxFuidRequestModel, profile);
                    logger.info("Response >>> saveTrxFuidRequestProspera {}", gson.toJson(response.getDetails()));

                    return ResponseEntity.ok(response);
                }
                logger.info("Response >>> saveTrxFuidRequestProspera : {}", getHttpStatusDetail(HttpStatus.BAD_REQUEST));
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).build();
            }
            logger.info("Response >>> saveTrxFuidRequestProspera : {}", getHttpStatusDetail(HttpStatus.TOO_MANY_REQUESTS));
            return ResponseEntity.status(HttpStatus.TOO_MANY_REQUESTS).build();
        } catch (Exception e) {
            logger.error("Fail to post saveTrxFuidRequestProspera ", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    private boolean isValidDataLength (TrxFuidRequestModel trxFuidRequestModel) {
        DataFuidModel data = trxFuidRequestModel.getFuid().getData();
        boolean isUserIdLengthValid = data.getUserId() == null || data.getUserId().length() <= 50;
        boolean isTeleponLengthValid = data.getTelepon() == null || data.getTelepon().length() <= 25;
        boolean isNamaVendorLengthValid = data.getNamaVendor() == null || data.getNamaVendor().length() <= 100;
        return data.getNamaLengkap().length() <= 100 &&
                data.getNIK().length() <= 40 &&
                data.getJabatan().length() <= 80 &&
                data.getKodeCabang().length() <= 10 &&
                data.getNamaCabang().length() <= 200 &&
                data.getEmail().length() <= 80 &&
                isUserIdLengthValid && isTeleponLengthValid && isNamaVendorLengthValid;
    }
}
